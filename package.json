{"name": "ems-backend", "version": "1.0.0", "type": "module", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1", "fetch-users": "node src/utils/fetchUsers.js"}, "keywords": ["ems", "backend", "api", "express"], "author": "EMS Team", "license": "ISC", "description": "Backend API server for Employee Management System", "dependencies": {"@sendgrid/mail": "^8.1.3", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "firebase-admin": "^12.7.0", "html-pdf": "^3.0.1", "mongoose": "^8.17.1", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^6.9.16", "pdfkit": "^0.15.0", "puppeteer": "^23.9.0"}, "devDependencies": {"nodemon": "^3.1.10"}}
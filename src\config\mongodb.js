import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

console.log('✅ Mongoose loaded successfully - MongoDB is the database');

// MongoDB connection configuration
const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_DB_NAME = process.env.MONGODB_DB_NAME || 'ems_database';

if (!MONGODB_URI) {
    console.error('❌ MONGODB_URI not found in environment variables. Please add it to your .env file.');
    console.error('   Example: MONGODB_URI=mongodb+srv://techcreator2019:<password>@ewbackend.wq8uaqx.mongodb.net/');
    process.exit(1); // Exit if MongoDB URI is not provided since it's now primary
}

// Connection options
const mongoOptions = {
    dbName: MONGODB_DB_NAME,
    maxPoolSize: 10, // Maintain up to 10 socket connections
    serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
    socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
    bufferCommands: false, // Disable mongoose buffering
    bufferMaxEntries: 0, // Disable mongoose buffering
};

// MongoDB connection state
let isConnected = false;

// Connect to MongoDB
export const connectMongoDB = async () => {
    if (isConnected) {
        console.log('✅ MongoDB already connected');
        return true;
    }

    try {
        console.log('🔄 Connecting to MongoDB...');
        console.log(`📍 URI: ${MONGODB_URI.replace(/\/\/[^:]+:[^@]+@/, '//***:***@')}`); // Hide credentials

        await mongoose.connect(MONGODB_URI, mongoOptions);

        isConnected = true;
        console.log('✅ MongoDB connected successfully');
        console.log(`📍 Database: ${MONGODB_DB_NAME}`);

        return true;
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error.message);
        console.error('❌ Cannot start server without MongoDB connection');
        process.exit(1); // Exit if MongoDB connection fails since it's now primary
    }
};

// Disconnect from MongoDB
export const disconnectMongoDB = async () => {
    if (!isConnected) {
        return;
    }

    try {
        await mongoose.disconnect();
        isConnected = false;
        console.log('✅ MongoDB disconnected successfully');
    } catch (error) {
        console.error('❌ MongoDB disconnection failed:', error.message);
    }
};

// Test MongoDB connection
export const testMongoConnection = async () => {
    try {
        const connection = await connectMongoDB();
        if (connection) {
            // Test with a simple ping
            await mongoose.connection.db.admin().ping();
            console.log('✅ MongoDB connection test successful');
            return true;
        }
        return false;
    } catch (error) {
        console.error('❌ MongoDB connection test failed:', error.message);
        process.exit(1); // Exit if test fails since MongoDB is now primary
    }
};

// Get MongoDB connection status
export const getMongoStatus = () => {
    return {
        isConnected,
        readyState: mongoose.connection.readyState,
        host: mongoose.connection.host,
        name: mongoose.connection.name,
        database: MONGODB_DB_NAME,
        states: {
            0: 'disconnected',
            1: 'connected',
            2: 'connecting',
            3: 'disconnecting'
        }
    };
};

// Handle connection events
mongoose.connection.on('connected', () => {
    console.log('🔗 MongoDB connection established');
    isConnected = true;
});

mongoose.connection.on('error', (error) => {
    console.error('❌ MongoDB connection error:', error);
    isConnected = false;
    process.exit(1); // Exit on connection error since MongoDB is required
});

mongoose.connection.on('disconnected', () => {
    console.log('🔌 MongoDB disconnected');
    isConnected = false;
});

// Handle process termination
process.on('SIGINT', async () => {
    console.log('🔄 Gracefully shutting down MongoDB connection...');
    await disconnectMongoDB();
    process.exit(0);
});

export default mongoose;

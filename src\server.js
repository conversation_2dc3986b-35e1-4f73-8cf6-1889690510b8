import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import cron from "node-cron";
import fetch from "node-fetch";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Try to import firebase-admin, but don't fail if it's not available
let admin = null;
try {
    const firebaseAdmin = await import("firebase-admin");
    admin = firebaseAdmin.default;
} catch (error) {
    console.log("⚠️ Firebase Admin SDK not available. Install with: npm install firebase-admin");
}

// Import configuration
import { connectMongoDB, testMongoConnection } from './config/mongodb.js';

// Import routes
import notificationRoutes from './routes/notifications.js';
import slackRoutes from './routes/slack.js';
import emailRoutes from './routes/email.js';
import pdfRoutes from './routes/pdf.js';
import mongodbRoutes from './routes/mongodb.js';

// Convert ES module URL to file path
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// Read Firebase credentials (optional)
let serviceAccount = null;
let firebaseInitialized = false;

if (admin) {
    try {
        serviceAccount = JSON.parse(fs.readFileSync(path.join(__dirname, "config/firebase-admin-sdk.json"), "utf8"));
        // Initialize Firebase Admin SDK
        admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
        });
        firebaseInitialized = true;
        console.log("✅ Firebase Admin SDK initialized successfully");
    } catch (error) {
        console.log("⚠️ Firebase Admin SDK not initialized (file not found). Push notifications will be disabled.");
        console.log("📝 To enable push notifications, add firebase-admin-sdk.json file to src/config/");
    }
} else {
    console.log("⚠️ Firebase Admin SDK not available. Install with: npm install firebase-admin");
}

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});

console.log("🚀 Starting server...");
console.log("📡 Supabase URL:", process.env.VITE_SUPABASE_URL ? "✅ Set" : "❌ Missing");
console.log("🔑 Slack Bot Token:", process.env.VITE_SLACK_BOT_USER_OAUTH_TOKEN ? "✅ Set" : "❌ Missing");

// Use routes
app.use('/api/notifications', notificationRoutes);
app.use('/api/slack', slackRoutes);
app.use('/api/email', emailRoutes);
app.use('/api/pdf', pdfRoutes);
app.use('/api/mongodb', mongodbRoutes);

// Legacy routes for backward compatibility (direct routes without /api prefix)
app.use('/', emailRoutes); // This allows /inviteClient and /sendtaskemail to work

// Health check endpoint
app.get('/health', async (req, res) => {
    try {
        // Import here to avoid circular dependency
        const { getMongoStatus } = await import('./config/mongodb.js');
        const mongoStatus = getMongoStatus();

        res.json({
            status: 'OK',
            message: 'EMS Backend Server - MongoDB Database',
            timestamp: new Date().toISOString(),
            database: {
                mongodb: {
                    status: mongoStatus.isConnected ? 'connected' : 'disconnected',
                    details: mongoStatus
                }
            },
            services: {
                firebase: firebaseInitialized ? 'initialized' : 'not initialized'
            }
        });
    } catch (error) {
        res.status(500).json({
            status: 'ERROR',
            message: 'Health check failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Test webhook endpoint
app.get("/test-webhook", (req, res) => {
    console.log("🧪 Test webhook endpoint called");
    res.json({ message: "Webhook endpoint is working!", timestamp: new Date().toISOString() });
});

// Function to send Checkin And CheckOut Reminders On Slack
const sendSlackNotification = async (message) => {
    const SLACK_WEBHOOK_URL = process.env.VITE_SLACK_WEBHOOK_URL;

    if (!SLACK_WEBHOOK_URL) {
        console.error("Slack Webhook URL is missing!");
        return;
    }

    try {
        const response = await fetch(SLACK_WEBHOOK_URL, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ text: message }),
        });

        if (!response.ok) throw new Error("Failed to send Slack notification");

        console.log("Notification sent successfully!");
    } catch (error) {
        console.error("Error sending notification:", error);
    }
};

// Schedule tasks using cron
cron.schedule("45 8 * * *", () => {
    sendSlackNotification("🌞 Good Morning! Please Don't Forget To Check In.");
}, {
    timezone: "Asia/Karachi"
});

cron.schedule("45 16 * * *", () => {
    sendSlackNotification("Hello Everyone! Ensure You Have Checked Out From EMS.");
}, {
    timezone: "Asia/Karachi"
});

cron.schedule("45 12 * * *", () => {
    sendSlackNotification("🔔 Reminder: Please Dont Forget To start Break!");
}, {
    timezone: "Asia/Karachi"
});

cron.schedule("45 13 * * *", () => {
    sendSlackNotification("🔔 Reminder: Please Dont Forget To End Break!");
}, {
    timezone: "Asia/Karachi"
});

// Start server
const startServer = async () => {
    try {
        // Test MongoDB connection
        console.log('🔄 Testing MongoDB connection...');

        // Connect and test MongoDB
        const mongoConnected = await connectMongoDB();
        if (mongoConnected) {
            await testMongoConnection();
        }

        app.listen(PORT, () => {
            console.log('');
            console.log('🚀 ===== EMS BACKEND SERVER STARTED =====');
            console.log(`📍 Server: http://localhost:${PORT}`);
            console.log(`🔍 Health: http://localhost:${PORT}/health`);
            console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
            console.log('');
            console.log('📊 Database Status:');
            console.log(`   MongoDB: ${mongoConnected ? '✅ Connected' : '❌ Failed'}`);
            console.log('');
            console.log('🔗 API Endpoints:');
            console.log('   /api/mongodb      - MongoDB operations');
            console.log('   /api/notifications - Push notifications');
            console.log('   /api/slack        - Slack integration');
            console.log('   /api/email        - Email services');
            console.log('   /api/pdf          - PDF generation');
            console.log('');
            console.log('🎯 Ready to serve requests with MongoDB DATABASE!');
            console.log('==========================================');
        });
    } catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
};

startServer();

// Export app for testing
export default app;
